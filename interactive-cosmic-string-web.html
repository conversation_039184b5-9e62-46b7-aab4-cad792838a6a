<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>互动宇宙弦网 | Interactive Cosmic String Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            background-color: #000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        #canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        canvas {
            display: block;
        }
        
        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.7);
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            max-width: 350px;
            z-index: 10;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        h1 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #fff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        p {
            font-size: 0.9rem;
            margin: 8px 0;
            line-height: 1.4;
        }
        
        .mode-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 15px;
            border-radius: 20px;
            z-index: 10;
            font-size: 0.9rem;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .mode-indicator span {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .gravity-mode span {
            background: #4dabf7;
            box-shadow: 0 0 8px #4dabf7;
        }
        
        .stats {
            position: fixed;
            top: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.7);
            z-index: 10;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="cosmicCanvas"></canvas>
    </div>
    
    <div class="mode-indicator gravity-mode">
        <span></span>
        引力模式 - 移动鼠标吸引粒子
    </div>
    
    <div class="stats">
        粒子: <span id="particle-count">100</span> | 
        弦连接: <span id="connection-count">0</span>
    </div>
    
    <div class="info-panel">
        <h1>互动宇宙弦网</h1>
        <p>在黑暗的宇宙空间中，发光的星尘粒子相互连接形成能量弦网络</p>
        <p>▶ <strong>引力模式</strong>: 移动鼠标吸引粒子形成星云</p>
        <p>▶ <strong>斥力爆发</strong>: 点击鼠标产生超新星爆发推开粒子</p>
        <p>粒子距离越近，连接弦越明亮；距离越远，弦越透明</p>
        <p style="margin-top: 10px; font-size: 0.8rem; opacity: 0.6;">基于Canvas的粒子物理模拟 | 原生JavaScript实现</p>
    </div>

    <script>
        // 主程序
        const canvas = document.getElementById('cosmicCanvas');
        const ctx = canvas.getContext('2d');
        
        // 调整Canvas为全屏
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        // 粒子类
        class Particle {
            constructor() {
                this.reset();
                this.radius = Math.random() * 1.5 + 0.5;
                this.baseHue = Math.random() * 60 + 200; // 蓝色调
            }
            
            reset() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.vx = (Math.random() - 0.5) * 0.7;
                this.vy = (Math.random() - 0.5) * 0.7;
                this.originalSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                this.connections = [];
            }
            
            update() {
                // 保存原始速度方向
                const originalAngle = Math.atan2(this.vy, this.vx);
                
                // 应用边界反弹
                const bounceFactor = 0.3;
                if (this.x < 0) {
                    this.x = 0;
                    this.vx = Math.abs(this.vx) * bounceFactor;
                } else if (this.x > canvas.width) {
                    this.x = canvas.width;
                    this.vx = -Math.abs(this.vx) * bounceFactor;
                }
                
                if (this.y < 0) {
                    this.y = 0;
                    this.vy = Math.abs(this.vy) * bounceFactor;
                } else if (this.y > canvas.height) {
                    this.y = canvas.height;
                    this.vy = -Math.abs(this.vy) * bounceFactor;
                }
                
                // 更新位置
                this.x += this.vx;
                this.y += this.vy;
                
                // 维持原始速度大小
                const currentSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                if (currentSpeed > 0) {
                    const speedRatio = this.originalSpeed / currentSpeed;
                    this.vx *= speedRatio;
                    this.vy *= speedRatio;
                }
            }
            
            draw() {
                // 粒子发光效果
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius * 3, 0, Math.PI * 2);
                const gradient = ctx.createRadialGradient(
                    this.x, this.y, 0,
                    this.x, this.y, this.radius * 3
                );
                gradient.addColorStop(0, `hsla(${this.baseHue}, 100%, 80%, 0.8)`);
                gradient.addColorStop(1, `hsla(${this.baseHue}, 100%, 80%, 0)`);
                ctx.fillStyle = gradient;
                ctx.fill();
                
                // 粒子核心
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'white';
                ctx.fill();
            }
        }
        
        // 宇宙参数
        const PARTICLES_COUNT = 100;
        const CONNECTION_DISTANCE = 150;
        const GRAVITY_FORCE = 0.02;
        const REPULSION_FORCE = 30;
        const REPULSION_RADIUS = 200;
        
        // 创建粒子
        const particles = [];
        for (let i = 0; i < PARTICLES_COUNT; i++) {
            particles.push(new Particle());
        }
        
        // 鼠标交互
        let mouseX = null;
        let mouseY = null;
        let repulsionEvent = null;
        
        // 事件监听
        canvas.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseleave', () => {
            mouseX = null;
            mouseY = null;
        });
        
        canvas.addEventListener('click', (e) => {
            repulsionEvent = {
                x: e.clientX,
                y: e.clientY,
                time: Date.now(),
                radius: 0
            };
            
            // 应用斥力
            particles.forEach(p => {
                const dx = p.x - repulsionEvent.x;
                const dy = p.y - repulsionEvent.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < REPULSION_RADIUS) {
                    const force = (REPULSION_RADIUS - distance) / REPULSION_RADIUS;
                    const angle = Math.atan2(dy, dx);
                    
                    p.vx += Math.cos(angle) * force * REPULSION_FORCE;
                    p.vy += Math.sin(angle) * force * REPULSION_FORCE;
                }
            });
        });
        
        // 绘制连接弦
        function drawConnections() {
            let connectionCount = 0;
            
            for (let i = 0; i < particles.length; i++) {
                particles[i].connections = [];
                
                for (let j = i + 1; j < particles.length; j++) {
                    const p1 = particles[i];
                    const p2 = particles[j];
                    
                    const dx = p1.x - p2.x;
                    const dy = p1.y - p2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < CONNECTION_DISTANCE) {
                        connectionCount++;
                        const opacity = 1 - distance / CONNECTION_DISTANCE;
                        
                        // 存储连接信息（用于绘制）
                        p1.connections.push({
                            target: p2,
                            distance: distance,
                            opacity: opacity
                        });
                        
                        // 绘制弦
                        ctx.beginPath();
                        ctx.moveTo(p1.x, p1.y);
                        ctx.lineTo(p2.x, p2.y);
                        
                        // 根据距离设置弦的颜色和透明度
                        const hue = p1.baseHue * 0.6 + p2.baseHue * 0.4;
                        ctx.strokeStyle = `hsla(${hue}, 70%, 70%, ${opacity * 0.6})`;
                        ctx.lineWidth = opacity * 1.5;
                        ctx.stroke();
                    }
                }
            }
            
            // 更新统计信息
            document.getElementById('connection-count').textContent = connectionCount;
        }
        
        // 动画循环
        function animate() {
            // 拖尾效果（用半透明黑色覆盖）
            ctx.fillStyle = 'rgba(0, 0, 0, 0.08)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 应用引力
            if (mouseX !== null && mouseY !== null) {
                particles.forEach(p => {
                    const dx = mouseX - p.x;
                    const dy = mouseY - p.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > 5) {
                        const angle = Math.atan2(dy, dx);
                        p.vx += Math.cos(angle) * GRAVITY_FORCE;
                        p.vy += Math.sin(angle) * GRAVITY_FORCE;
                    }
                });
            }
            
            // 更新粒子
            particles.forEach(p => p.update());
            
            // 绘制连接弦
            drawConnections();
            
            // 绘制粒子
            particles.forEach(p => p.draw());
            
            // 绘制斥力效果
            if (repulsionEvent) {
                const elapsed = Date.now() - repulsionEvent.time;
                repulsionEvent.radius = elapsed * 0.3;
                
                if (elapsed < 1000) {
                    ctx.beginPath();
                    ctx.arc(repulsionEvent.x, repulsionEvent.y, repulsionEvent.radius, 0, Math.PI * 2);
                    const gradient = ctx.createRadialGradient(
                        repulsionEvent.x, repulsionEvent.y, 0,
                        repulsionEvent.x, repulsionEvent.y, repulsionEvent.radius
                    );
                    gradient.addColorStop(0, 'rgba(255, 100, 100, 0.8)');
                    gradient.addColorStop(1, 'rgba(255, 100, 100, 0)');
                    ctx.fillStyle = gradient;
                    ctx.fill();
                } else {
                    repulsionEvent = null;
                }
            }
            
            requestAnimationFrame(animate);
        }
        
        // 初始化
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
        animate();
        
        // 显示粒子数量
        document.getElementById('particle-count').textContent = PARTICLES_COUNT;
    </script>
</body>
</html>