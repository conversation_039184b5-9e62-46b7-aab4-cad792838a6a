<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>互动宇宙弦网 | Interactive Cosmic String Web</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            background-color: #000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        #canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        canvas {
            display: block;
        }
        

    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="cosmicCanvas"></canvas>
    </div>
    


    <script>
        // 主程序
        const canvas = document.getElementById('cosmicCanvas');
        const ctx = canvas.getContext('2d');
        
        // 调整Canvas为全屏
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        // 粒子类
        class Particle {
            constructor() {
                this.reset();
                this.radius = Math.random() * 1.5 + 1; // 稍微大一点的粒子
            }

            reset() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.vx = (Math.random() - 0.5) * 0.5; // 更慢的初始速度，模拟缓慢漂移
                this.vy = (Math.random() - 0.5) * 0.5;
                this.originalSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                this.connections = [];
            }
            
            update() {
                // 保存原始速度方向
                const originalAngle = Math.atan2(this.vy, this.vx);
                
                // 应用边界反弹
                const bounceFactor = 0.3;
                if (this.x < 0) {
                    this.x = 0;
                    this.vx = Math.abs(this.vx) * bounceFactor;
                } else if (this.x > canvas.width) {
                    this.x = canvas.width;
                    this.vx = -Math.abs(this.vx) * bounceFactor;
                }
                
                if (this.y < 0) {
                    this.y = 0;
                    this.vy = Math.abs(this.vy) * bounceFactor;
                } else if (this.y > canvas.height) {
                    this.y = canvas.height;
                    this.vy = -Math.abs(this.vy) * bounceFactor;
                }
                
                // 更新位置
                this.x += this.vx;
                this.y += this.vy;
                
                // 维持原始速度大小
                const currentSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                if (currentSpeed > 0) {
                    const speedRatio = this.originalSpeed / currentSpeed;
                    this.vx *= speedRatio;
                    this.vy *= speedRatio;
                }
            }
            
            draw() {
                // 使用shadowBlur创建辉光效果
                ctx.save();
                ctx.shadowColor = 'white';
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;

                // 绘制明亮的白色粒子
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'white';
                ctx.fill();

                ctx.restore();
            }
        }
        
        // 宇宙参数
        const PARTICLES_COUNT = 100;
        const CONNECTION_DISTANCE = 150;
        const GRAVITY_FORCE = 0.02;
        const REPULSION_FORCE = 30;
        const REPULSION_RADIUS = 200;
        
        // 创建粒子
        const particles = [];
        for (let i = 0; i < PARTICLES_COUNT; i++) {
            particles.push(new Particle());
        }
        
        // 鼠标交互
        let mouseX = null;
        let mouseY = null;
        let repulsionEvent = null;
        
        // 事件监听
        canvas.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseleave', () => {
            mouseX = null;
            mouseY = null;
        });
        
        canvas.addEventListener('click', (e) => {
            repulsionEvent = {
                x: e.clientX,
                y: e.clientY,
                time: Date.now(),
                radius: 0
            };
            
            // 应用斥力
            particles.forEach(p => {
                const dx = p.x - repulsionEvent.x;
                const dy = p.y - repulsionEvent.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < REPULSION_RADIUS) {
                    const force = (REPULSION_RADIUS - distance) / REPULSION_RADIUS;
                    const angle = Math.atan2(dy, dx);
                    
                    p.vx += Math.cos(angle) * force * REPULSION_FORCE;
                    p.vy += Math.sin(angle) * force * REPULSION_FORCE;
                }
            });
        });
        
        // 绘制连接弦
        function drawConnections() {
            let connectionCount = 0;
            
            for (let i = 0; i < particles.length; i++) {
                particles[i].connections = [];
                
                for (let j = i + 1; j < particles.length; j++) {
                    const p1 = particles[i];
                    const p2 = particles[j];
                    
                    const dx = p1.x - p2.x;
                    const dy = p1.y - p2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < CONNECTION_DISTANCE) {
                        connectionCount++;
                        const opacity = 1 - distance / CONNECTION_DISTANCE;
                        
                        // 存储连接信息（用于绘制）
                        p1.connections.push({
                            target: p2,
                            distance: distance,
                            opacity: opacity
                        });
                        
                        // 绘制纤细的白色弦
                        ctx.beginPath();
                        ctx.moveTo(p1.x, p1.y);
                        ctx.lineTo(p2.x, p2.y);

                        // 纯白色弦，透明度根据距离变化
                        ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.7})`;
                        ctx.lineWidth = 0.5; // 纤细的线条
                        ctx.stroke();
                    }
                }
            }
            

        }
        
        // 动画循环
        function animate() {
            // 拖尾效果（用低透明度黑色覆盖，创造彗星轨迹效果）
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 应用引力
            if (mouseX !== null && mouseY !== null) {
                particles.forEach(p => {
                    const dx = mouseX - p.x;
                    const dy = mouseY - p.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > 5) {
                        const angle = Math.atan2(dy, dx);
                        p.vx += Math.cos(angle) * GRAVITY_FORCE;
                        p.vy += Math.sin(angle) * GRAVITY_FORCE;
                    }
                });
            }
            
            // 更新粒子
            particles.forEach(p => p.update());
            
            // 绘制连接弦
            drawConnections();
            
            // 绘制粒子
            particles.forEach(p => p.draw());
            
            // 绘制斥力效果
            if (repulsionEvent) {
                const elapsed = Date.now() - repulsionEvent.time;
                repulsionEvent.radius = elapsed * 0.3;
                
                if (elapsed < 1000) {
                    ctx.beginPath();
                    ctx.arc(repulsionEvent.x, repulsionEvent.y, repulsionEvent.radius, 0, Math.PI * 2);
                    const gradient = ctx.createRadialGradient(
                        repulsionEvent.x, repulsionEvent.y, 0,
                        repulsionEvent.x, repulsionEvent.y, repulsionEvent.radius
                    );
                    gradient.addColorStop(0, 'rgba(255, 100, 100, 0.8)');
                    gradient.addColorStop(1, 'rgba(255, 100, 100, 0)');
                    ctx.fillStyle = gradient;
                    ctx.fill();
                } else {
                    repulsionEvent = null;
                }
            }
            
            requestAnimationFrame(animate);
        }
        
        // 初始化
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
        animate();
    </script>
</body>
</html>